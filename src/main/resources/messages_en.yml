# English Language File for CAH Plugin

# General Messages
general:
  player_only: "&c&lThis command can only be used by players!"
  no_permission: "&c<PERSON>ou don't have permission to use this command."
  player_not_found: "&c&lPlayer not found!"
  invalid_usage: "&cIncorrect command usage."
  success: "&aCommand executed successfully!"
  error: "&cAn error occurred while executing the command."
  under_construction: "&c&lUnder construction!"

# Join/Quit Messages
join:
  message: "&7[&a+&7] &a{0}"
  welcome_title: "&6&lCraftAndHelps"
  welcome_subtitle: "&a&lWelcome!"
  tab_header: "&b&lYou are playing on &6&lCraftAndHelps"
  tab_footer: "&7Website: &fcraftandhelps.com"

quit:
  message: "&7[&c-&7] &c{0}"

# Groups Command
groups:
  title: "&6&lRanks &8» &7Commands:"
  set_usage: "&6&lRanks &8» &7/grupo setar <player> <groupid> &f- &7Set a player's group."
  list_usage: "&6&lRanks &8» &7/grupo lista &f- &7List groups."
  set_success: "&6&lRanks &8» &7Player {0}'s group set to {1}."
  invalid_group: "&6&lRanks &8» &7Invalid group ID."

# Kit Command
kit:
  player_only: "&cCommand for players only."
  rank_kit: "&6&lRank Kit"
  vip_kit: "&6&lVIP Kit"
  pvp_kit: "&6&lPvP Kit"
  cooldown_message: "&cWait {0}h {1}m {2}s to use this kit again."
  no_permission_kit: "&cYou don't have permission to use this kit."
  received_pvp: "&aYou received the PvP kit."
  cooldown: "&cWait {0}h {1}m {2}s to use this kit again."
  no_permission: "&cYou don't have permission to use this kit."
  pvp_received: "&aYou received the PvP kit."

# Money Command
money:
  title: "&6&lMoney &8»"
  balance: "&7Your balance: &a{0}€"
  balance_other: "&7{0}'s balance: &a{1}€"
  no_permission: "&6&lMoney &8» &7You don't have permission for this command."
  usage_set: "&6&lMoney &8» &7Use: /money set <player> <amount>"
  usage_add: "&6&lMoney &8» &7Use: /money add <player> <amount>"
  usage_remove: "&6&lMoney &8» &7Use: /money remove <player> <amount>"
  usage_send: "&6&lMoney &8» &7Use: /money send <player> <amount>"
  insufficient_funds: "&6&lMoney &8» &7You don't have enough money."
  player_only_send: "&6&lMoney &8» &7This command can only be executed by a player."
  unknown_command: "&6&lMoney &8» &7Unknown command. Use \"/money help\""
  success_transaction: "&6&lMoney &8» &7Success. {0} now has {1}€."
  help_title: "&6&lMoney &8» &7Commands:"
  help_balance: "&6&lMoney &8» &7/money &f- &7Check your balance."
  help_balance_other: "&6&lMoney &8» &7/money <player> &f- &7Check another player's balance."
  help_send: "&6&lMoney &8» &7/money send <player> <amount> &f- &7Send money to another player."
  help_set: "&6&lMoney &8» &7/money set <player> <amount> &f- &7Set a player's money"
  help_add: "&6&lMoney &8» &7/money add <player> <amount> &f- &7Add money to a player."
  help_remove: "&6&lMoney &8» &7/money remove <player> <amount> &f- &7Remove money from a player."
  invalid: "&6&lMoney &8» &7Invalid."
  player_not_found: "&6&lMoney &8» &7Unknown player."
  other_balance: "&6&lMoney &8» &7{0} has {1}€."
  invalid_number: "&6&lMoney &8» &7Number not recognized."
  transaction_success: "&6&lMoney &8» &7Success. {0} now has {1}€."
  help:
    header: "&6&lMoney &8» &7Commands:"
    balance: "&6&lMoney &8» &7/money &f- &7Check your balance."
    other_balance: "&6&lMoney &8» &7/money <player> &f- &7Check another player's balance."
    send: "&6&lMoney &8» &7/money send <player> <amount> &f- &7Send money to another player."
    set: "&6&lMoney &8» &7/money set <player> <amount> &f- &7Set a player's money"
    add: "&6&lMoney &8» &7/money add <player> <amount> &f- &7Add money to a player."
    remove: "&6&lMoney &8» &7/money remove <player> <amount> &f- &7Remove money from a player."

# DarCaixa Command
darcaixa:
  usage: "&c&lUse /darcaixa <player> <type>"
  success: "&a&lYou gave a {0} box to {1}"
  success_console: "&a&lYou gave a {0} key to {1}"
  box_name: "&a&l{0} Box"
  box_lore: "&7{0} box"
  no_permission: "&cOnly players with &6[DEV] &cgroup or higher can use this command."

# SetarKit Command
setarkit:
  usage: "&6&lKits &8- &cUse /setarkit <type> <level> <cooldownMinutes>"
  no_permission: "&6&lKits &8- &cYou don't have permission to use this command."
  success: "&6&lKits &8- &aKit set to {0} {1} with cooldown of {2} minutes."

# Chat Messages
chat:
  usage: "&c/chat <global|local|clan> - Change chat mode."
  muted: "&cYou are muted."
  hover_name: "&6{0}\n"
  hover_prestige: "&6Prestige: &e{0}\n"
  hover_rank: "&fRole: {0}\n"
  hover_rank_display: "&dRank: {0}\n"
  hover_money: "&cMoney: {0}€"
  login_required: "&cLogin or register to be able to chat."
  mode_not_exists_global: "&cThis chat mode doesn't exist. Switching to global."
  not_in_clan_global: "&cYou're not in a clan. Switching to global."
  mode_changed: "&7Chat mode changed to &f{0}&7."
  hover:
    prestige: "&6Prestige: &e{0}"
    role: "&fRole: {0}"
    rank: "&dRank: {0}"
    money: "&cMoney: {0}€"

# Clan Commands
clan:
  title: "&6&lClans &7&l>"
  usage: "&6&lClans &7&l> Use: /clan <create|invite|join|leave|delete> [name]"
  create_usage: "&6&lClans &7&l> Use: /clan create <name>"
  invite_usage: "&6&lClans &7&l> Use: /clan invite <name>"
  join_usage: "&6&lClans &7&l> Use: /clan join <name>"
  player_not_online: "&6&lClans &7&l> The player is not online."
  already_in_clan: "&6&lClans &7&l> You're already in a clan."
  clan_exists: "&6&lClans &7&l> A clan with that name already exists."
  clan_created: "&6&lClans &7&l> Clan {0} created successfully."
  created: "&6&lClans &7&l> Clan created: {0}"
  name_exists: "&6&lClans &7&l> The clan name already exists."
  player_invited: "&6&lClans &7&l> {0} was invited to the clan."
  not_clan_leader: "&6&lClans &7&l> Only the clan leader can invite players."
  leader_only: "&6&lClans &7&l> Only the clan leader can invite players."
  joined_clan: "&6&lClans &7&l> You joined clan {0}"
  no_invite: "&6&lClans &7&l> You don't have an invite to join clan {0}"
  not_in_clan: "&6&lClans &7&l> You're not in a clan."
  cannot_leave_leader: "&6&lClans &7&l> You can't leave the clan because you're the leader."
  left_clan: "&6&lClans &7&l> You left the clan."
  only_leader_delete: "&6&lClans &7&l> Only the clan leader can delete the clan."
  clan_deleted: "&6&lClans &7&l> Clan deleted."

# Ban Messages
ban:
  usage: "&6&lStaff &8 - &cCorrect usage: /banir <player> <time> <reason>"
  unban_usage: "&6&lStaff &8 - &cCorrect usage: /desbanir <player>"
  player_not_online: "&6&lStaff &8 - &cThis player is not online."
  player_banned: "&6&lStaff &8 - &aPlayer {0} was banned for {1}"
  player_unbanned: "&6&lStaff &8 - &aPlayer {0} was unbanned."
  banned_message: "&r&6&lCraftAndHelps&r\n\n&r&cYou have been banned.&r\n\n&r&cReason: &a{0}&r\n\n&r&cTime remaining: {1}&r"
  use_banir: "&6&lStaff &8 - &cUse the /ban command"
  use_mutar: "&6&lStaff &8 - &cUse the /mute command"
  use_desbanir: "&6&lStaff &8 - &cUse the /unban command"
  use_desmutar: "&6&lStaff &8 - &cUse the /unmute command"

# Login/Register Messages
login:
  register_usage: "&cUsage: /registar <password>"
  login_usage: "&cUsage: /login <password>"
  change_password_usage: "&cUsage: /mudarpassword <newpassword>"
  already_registered: "&cYou're already registered. Use /login <password> to log in."
  register_success: "&aRegistered successfully!"
  login_title: "&c&lLOGIN"
  login_subtitle: "&a&lUse /login <password> to enter"
  not_registered: "&cYou're not registered. Use /register <password> to register."
  wrong_password: "&cWrong password."
  login_success: "&aLogged in successfully!"
  welcome_title_login: "&6&lCAH"
  welcome_subtitle_login: "&a&lWelcome!"
  login_required_commands: "&cLogin or register to be able to use commands."
  login_required_move: "&cLogin or register to be able to move."
  under_penalty: "&cYou're in combat. Wait {0} seconds to execute commands."
  no_commands_tournament: "&cYou can't use commands during the tournament!"

# VIP Messages
vip:
  expired_kick: "&r&aVIP expired!&r\n&r&bYou were kicked so permissions get updated&r"

# SkyWars Messages
skywars:
  create_usage: "&c/skywars create <name>"
  created_success: "&aSkyWars created successfully!"
  already_exists: "&cThat skywars already exists!"
  addkit_usage: "&c/skywars addkit <name>"

# Prestige Messages
prestige:
  title_announcement: "&6Prestige!"
  subtitle_announcement: "&a{0} has prestiged!"
  success_message: "&aCongratulations! You have prestiged and are now at level {0}!"
  not_max_rank: "&cYou need to be at max rank (God) to prestige."

# MOTD
motd: "&l   &6                 CraftAndHelps &7[1.8]&r\n&l &b&l        Official Reopening &8| &4Rankup 1.0"

# Language Command
language:
  title: "&6&lLanguage &8»"
  current: "&7Your current language: &a{0}"
  changed: "&aLanguage changed to: &6{0}"
  invalid: "&cInvalid language. Available languages: {0}"
  usage: "&7Use: /language <pt|en>"
  available: "&7Available languages: &a{0}"

# Scoreboard
scoreboard:
  nick: "&aNick: {0}"
  rank: "&dRank: {0}"
  money: "&cMoney: {0}€"
  online: "&bOnline: {0}"
  kills: "&4Kills: {0}"
  deaths: "&fDeaths: {0}"
  blocks: "&5Blocks: {0}"
  time: "&7Time: {0}"
  website: "&7craftandhelps.com"

# Votar command
votar:
  available_soon: "&6&lVote &7» &a&lAvailable soon at craftandhelps.com"

# Achievement System
achievement:
  completed: "&a&lACHIEVEMENT UNLOCKED: &e{0}"
  not_loaded: "&cAchievement data not loaded yet. Please try again in a moment."
  none_available: "&cNo achievements available."
  list_header: "&6&l=== YOUR ACHIEVEMENTS ==="
  list_footer: "&6Completed: &a{0}&6/&e{1} &6achievements"

  # GUI Messages
  gui:
    title: "&6&lAchievements"
    category_title: "&6&lAchievements - {0}"
    click_to_view: "&eClick to view achievements!"
    no_achievements_title: "&c&lNo Achievements"
    no_achievements_desc1: "&7This category has no"
    no_achievements_desc2: "&7available achievements."
    back_button: "&c&lBack"
    back_button_desc: "&7Click to return to categories"

  # Category Names
  categories:
    bronze: "Bronze"
    silver: "Silver"
    gold: "Gold"
    platinum: "Platinum"
    mythic: "Mythic"

  # Progress Messages
  progress:
    achievements: "&7Achievements: &e{0}&7/&e{1}"
    progress_percent: "&7Progress: &a{0}%"
    current_progress: "&7Progress: &e{0}&7/&e{1}"
    completed: "&a&lCOMPLETED!"
    completed_on: "&7Completed on: &a{0}"
    rewards_title: "&6Rewards:"
    reward_money: "&7• &e{0}€"
    reward_broadcast: "&7• &dGlobal announcement"
    reward_title: "&7• &bSpecial title"
    reward_special: "&7• &fSpecial command"

# Rank command
rank:
  current: "&6&lRank &8» &7Your current rank is: {0}"
  max_rank: "&6&lRank &8» &7You are already at the maximum rank."
  need_money: "&6&lRank &8» &7To rankup you need {0}€."
  enough_money: "&6&lRank &8» &7You already have enough money to rankup."

# Rankup command
rankup:
  max_rank: "&6&lRankup &8» &7You are already at the maximum rank."
  success: "&6&lRankup &8» &7Rankup completed successfully."
  insufficient_funds: "&6&lRankup &8» &7You don't have enough money to rankup. You need {0}€ more."

# Private message command
privatemsg:
  usage: "&cCorrect usage: /{0} <player> <message>"
  received: "&a[Private message from {0}] &f» {1}"
  sent: "&a[Private message to {0}] &f» {1}"

# PvP Event command
pvpevent:
  help:
    title: "&6&l1v1 PvP Event Commands:"
    start: "&e/1v1 start &7- Start the event"
    setloc1: "&e/1v1 setloc1 &7- Set spawn location 1"
    setloc2: "&e/1v1 setloc2 &7- Set spawn location 2"
    lobby_set: "&e/1v1 lobby set &7- Set lobby location"
    join: "&e/1v1 join &7- Join the event"
    lobby: "&e/1v1 lobby &7- Teleport to event lobby"

# Mina Command
mina:
  teleported: "&a&lYou have been teleported to your rank's mine!"

# Warps Commands
warps:
  teleported_enchantments: "&aTeleported to enchantments!"
  teleported_minapvp: "&aTeleported to PvP mine!"
  teleported_hub: "&aTeleported to hub!"
  teleported_shop: "&aTeleported to shop!"
  teleported_spawn: "&aTeleported to spawn!"
  teleported_mines: "&aTeleported to mines!"
  teleported_plots: "&aTeleported to plots!"
  crate_development: "&c&lUnder development!"

# Reparar Command
reparar:
  success: "&6&lRepair &8» &7Item repaired successfully for &f{0}€"
  insufficient_funds: "&6&lRepair &8» &7You don't have enough € to repair this item."
  no_repair_needed: "&6&lRepair &8» &7This item doesn't need repair."

# Twitch Command
twitch:
  usage: "&cCorrect usage: /twitch <link|unlink|verify> [channel]"
  usage_link: "&cCorrect usage: /twitch link <channel>"
  usage_set: "&cCorrect usage: /twitch set <player> <channel>"
  channel_already_linked: "&cThis Twitch channel is already linked to another player."
  already_linked: "&cYou already have a Twitch channel linked."
  not_linked: "&cYou don't have any Twitch channel linked."
  verification_code: "&aVerification code generated: {0}"
  add_code_instruction: "&aAdd this code to your Twitch channel description to verify."
  unlinked_success: "&aYou have successfully unlinked your Twitch channel."
  set_success: "&aPlayer's Twitch channel set successfully."

# Stream Rewards
stream_rewards:
  book_reward: "&a&lStream Reward! &7You received an enchanted book for watching {0} for 30 minutes!"
  money_reward: "&a&lStream Reward! &7You received &6$50,000 &7for watching {0} for 1 hour!"
  key_reward: "&a&lStream Reward! &7You received a special key for watching {0} for 2 hours!"
  book_dropped: "&a&lStream Reward! &7An enchanted book was dropped on the ground for watching {0} for 30 minutes!"
  key_dropped: "&a&lStream Reward! &7A special key was dropped on the ground for watching {0} for 2 hours!"

# Stream Reward Command
streamreward:
  usage: "&c&lStream Reward Commands:"
  usage_check: "&7/streamreward check <player> <streamer> - Check watch time"
  usage_reset: "&7/streamreward reset <player> <streamer> - Reset rewards for player/streamer"
  usage_info: "&7/streamreward info - Show system info"
  check_usage: "&cUsage: /streamreward check <player> <streamer>"
  reset_usage: "&cUsage: /streamreward reset <player> <streamer>"
  player_not_found: "&cPlayer not found or not online."
  watch_time_info: "&a&lStream Watch Time Info:"
  player_label: "&7Player: &f{0}"
  streamer_label: "&7Streamer: &f{0}"
  watch_time_label: "&7Watch Time: &f{0}"
  rewards_status: "&7Rewards Status:"
  reward_30min: "  &730min (Book): {0}"
  reward_1hour: "  &71hour (Money): {0}"
  reward_2hours: "  &72hours (Key): {0}"
  reward_eligible: "&a✓ Eligible"
  reward_not_reached: "&c✗ Not reached"
  reset_success_offline: "&aReset rewards for offline player {0} and streamer {1}"
  reset_success_online: "&aReset rewards for {0} and streamer {1}"
  system_info: "&a&lStream Reward System Info:"
  live_streamers: "&7Live Streamers: &f{0}"
  streamer_list: "  &7- &f{0}"
  reward_thresholds: "&7Reward Thresholds:"
  threshold_30min: "  &730 minutes: &fEnchanted Book"
  threshold_1hour: "  &71 hour: &f$50,000"
  threshold_2hours: "  &72 hours: &fSpecial Key"
  unknown_subcommand: "&cUnknown subcommand. Use /streamreward for help."
  console_not_allowed: "&cOnly players can execute this command."
  time_seconds: "{0} seconds"
  time_minutes: "{0} minutes, {1} seconds"
  time_hours: "{0} hours, {1} minutes, {2} seconds"

# Portal Messages
portal:
  teleported: "&7You have been teleported."
  vip_god_required: "&cThis mine is exclusive to §7[VIP GOD]§c group or higher."

# Placas (Signs) listener
placas:
  enchantment_not_found: "&cEnchantment not found."
