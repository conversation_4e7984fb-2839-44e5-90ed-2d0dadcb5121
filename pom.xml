<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>me.miguel19877.dev</groupId>
    <artifactId>CAH</artifactId>
    <version>1.0</version>
    <packaging>jar</packaging>

    <name>CAH</name>

    <description>CAH</description>
    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <pluginRepositories>
        <pluginRepository>
            <id>maven2</id>
            <url>https://repo.maven.apache.org/maven2/</url>
        </pluginRepository>
    </pluginRepositories>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.2.4</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <createDependencyReducedPom>false</createDependencyReducedPom>
                            <outputFile>CAH.jar</outputFile>
                            <relocations>
                                <relocation>
                                    <pattern>redis.clients.jedis</pattern>
                                    <shadedPattern>me.miguel19877.dev.internal.jedis
                                    </shadedPattern>
                                </relocation>
                                <relocation>
                                    <pattern>net.jitse.npclib</pattern>
                                    <shadedPattern>me.miguel19877.dev.internal.npclib
                                    </shadedPattern>
                                </relocation>
                                <relocation>
                                    <pattern>fr.mrmicky.fastboard</pattern>
                                    <!-- Replace 'com.yourpackage' with the package of your plugin ! -->
                                    <shadedPattern>me.miguel19877.dev.fastboard</shadedPattern>
                                </relocation>
                            </relocations>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>

    <repositories>
        <repository>
            <id>dmulloy2-repo</id>
            <url>https://repo.dmulloy2.net/repository/public/</url>
        </repository>
        <repository>
            <id>spigot-repo</id>
            <url>https://hub.spigotmc.org/nexus/content/repositories/snapshots/</url>
        </repository>
        <repository>
            <id>ossrh</id>
            <url>https://oss.sonatype.org/content/groups/public/</url>
        </repository>
        <repository>
            <id>spigot</id>
            <url>https://repo.songoda.com/repository/public/</url>
        </repository>
        <repository>
            <id>jitpack.io</id>
            <url>https://jitpack.io</url>
        </repository>
        <repository>
            <id>hpfxd-releases</id>
            <name>hpfxd repo</name>
            <url>https://repo.hpfxd.com/releases</url>
        </repository>

    </repositories>

    <dependencies>
        <!-- https://mvnrepository.com/artifact/org.spigotmc/spigot -->
        <dependency>
            <groupId>org.spigotmc</groupId>
            <artifactId>spigot</artifactId>
            <version>1.8.8</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>fr.mrmicky</groupId>
            <artifactId>fastboard</artifactId>
            <version>2.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.comphenix.protocol</groupId>
            <artifactId>ProtocolLib</artifactId>
            <version>5.1.0</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.github.unldenis</groupId>
            <artifactId>Hologram-Lib</artifactId>
            <version>1.3.0</version>
        </dependency>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>3.6.0</version>
            <type>jar</type>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>net.jitse</groupId>
            <artifactId>npclib-plugin</artifactId>
            <version>2.11.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
            <version>2.4.2</version>
        </dependency>
        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>20231013</version>
        </dependency>
    </dependencies>
</project>
